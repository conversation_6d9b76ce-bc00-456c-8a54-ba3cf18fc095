package main

import (
	"fmt"
	"os"
	"strings"
)

const (
	// dev1 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev1_local_gin_url_base = "http://192.168.1.200:15445"
	// dev2 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev2_local_gin_url_base = "http://192.168.1.200:25906"
	// sit 服务器 内网 gin HTTP 地址
	sit_local_gin_url_base = "http://192.168.1.200:30000" // 端口号待定

	// dev1 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev1_local_rpc_url_base = "http://192.168.1.200:15446"
	// dev2 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev2_local_rpc_url_base = "http://192.168.1.200:25907"
	// sit 服务器 内网 RPC 地址(注意，在 k8s 上需映射)
	sit_local_rpc_url_base = "http://192.168.1.200:30001" // 端口号待定

	// dev1 服务器 外网地址
	dev1_external_url_base = "http://ny10wt9045294.vicp.fun:25639"
	// dev2 服务器 外网地址
	dev2_external_url_base = "http://ny10wt9045294.vicp.fun"
	// sit 服务器 外网地址
	sit_external_url_base = "http://ny10wt9045294.vicp.fun:29397"
)

func main() {
	run_env := "all"
	if len(os.Args) > 1 {
		run_env = os.Args[1]
	}
	if run_env == "all" {
		run_env = "dev1,dev2,sit"
	}

	run_envs := make([]string, 0)
	for _, env := range strings.Split(run_env, ",") {
		run_envs = append(run_envs, env)
	}

	for _, run_env := range run_envs {
		fmt.Printf("=-=-=-=-=-=-=-= Starting test for %v =-=-=-=-=-=-=-\n", run_env)
		StartTest(run_env)
	}
}

func StartTest(run_env string) {

}
