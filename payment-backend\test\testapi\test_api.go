package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

const (
	// dev1 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev1_local_gin_url_base = "http://192.168.1.200:15445"
	// dev2 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev2_local_gin_url_base = "http://192.168.1.200:25906"
	// sit 服务器 内网 gin HTTP 地址
	sit_local_gin_url_base = "http://192.168.1.200:30000" // 端口号待定

	// dev1 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev1_local_rpc_url_base = "http://192.168.1.200:15446"
	// dev2 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev2_local_rpc_url_base = "http://192.168.1.200:25907"
	// sit 服务器 内网 RPC 地址(注意，在 k8s 上需映射)
	sit_local_rpc_url_base = "http://192.168.1.200:30001" // 端口号待定

	// dev1 服务器 外网地址
	dev1_external_url_base = "http://ny10wt9045294.vicp.fun:25639"
	// dev2 服务器 外网地址
	dev2_external_url_base = "http://ny10wt9045294.vicp.fun"
	// sit 服务器 外网地址
	sit_external_url_base = "http://ny10wt9045294.vicp.fun:29397"
)

// 环境配置
type EnvConfig struct {
	Name        string
	ExternalURL string // 外网地址
	LocalGinURL string // 内网 gin HTTP 地址
	LocalRpcURL string // 内网 RPC 地址
}

// 测试结果
type TestResult struct {
	TestName string
	Success  bool
	Message  string
	Duration time.Duration
}

// 创建订单请求
type CreateOrderRequest struct {
	ProductID   string `json:"product_id"`
	ProductDesc string `json:"product_desc"`
	PriceID     string `json:"price_id"`
	Quantity    int    `json:"quantity"`
	PSPProvider string `json:"psp_provider"`
}

// 创建订单响应
type CreateOrderResponse struct {
	OrderID     string    `json:"order_id"`
	CheckoutURL string    `json:"checkout_url"`
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	ExpiresAt   time.Time `json:"expires_at"`
}

func main() {
	run_env := "all"
	if len(os.Args) > 1 {
		run_env = os.Args[1]
	}
	if run_env == "all" {
		run_env = "dev1,dev2,sit"
	}

	run_envs := make([]string, 0)
	for _, env := range strings.Split(run_env, ",") {
		run_envs = append(run_envs, strings.TrimSpace(env))
	}

	for _, run_env := range run_envs {
		fmt.Printf("=-=-=-=-=-=-=-= Starting test for %v =-=-=-=-=-=-=-\n", run_env)
		StartTest(run_env)
		fmt.Printf("--------------- Finished test for %v =-=-=-=-=-=-=-\n\n", run_env)
	}
}

func StartTest(run_env string) {
	config := getEnvConfig(run_env)
	if config == nil {
		fmt.Printf("Unknown environment: %s\n", run_env)
		return
	}

	fmt.Printf("Testing environment: %s\n", config.Name)
	fmt.Printf("External URL: %s\n", config.ExternalURL)
	fmt.Printf("Local Gin URL: %s\n", config.LocalGinURL)
	fmt.Printf("Local RPC URL: %s\n", config.LocalRpcURL)
	fmt.Println()

	var results []TestResult

	// 测试外网接口 (只测试 gin HTTP)
	fmt.Println("=== Testing External APIs (Gin HTTP only) ===")
	results = append(results, testCreateOrderExternal(config))
	results = append(results, testGetUserOrdersExternal(config))

	// 测试内网接口 (测试 gin HTTP 和 dubbo RPC)
	fmt.Println("\n=== Testing Internal APIs (Gin HTTP + Dubbo RPC) ===")
	results = append(results, testListAllOrdersGin(config))
	results = append(results, testListAllOrdersRPC(config))

	// 打印测试结果汇总
	printTestSummary(results)
}

// getEnvConfig 获取环境配置
func getEnvConfig(env string) *EnvConfig {
	switch env {
	case "dev1":
		return &EnvConfig{
			Name:        "dev1",
			ExternalURL: dev1_external_url_base,
			LocalGinURL: dev1_local_gin_url_base,
			LocalRpcURL: dev1_local_rpc_url_base,
		}
	case "dev2":
		return &EnvConfig{
			Name:        "dev2",
			ExternalURL: dev2_external_url_base,
			LocalGinURL: dev2_local_gin_url_base,
			LocalRpcURL: dev2_local_rpc_url_base,
		}
	case "sit":
		return &EnvConfig{
			Name:        "sit",
			ExternalURL: sit_external_url_base,
			LocalGinURL: sit_local_gin_url_base,
			LocalRpcURL: sit_local_rpc_url_base,
		}
	default:
		return nil
	}
}

// makeHTTPRequest 发送HTTP请求的通用函数
func makeHTTPRequest(method, url string, headers map[string]string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, err
	}

	// 设置默认头部
	req.Header.Set("Content-Type", "application/json")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	return client.Do(req)
}

// testCreateOrderExternal 测试外网创建订单接口
func testCreateOrderExternal(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Create Order (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/order-service/orders"
	headers := map[string]string{
		"x-trace-id":    "test_create_order_001",
		"x-user-id":     "user123",
		"x-role":        "customer",
		"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.eB4reqDA4b8-pjK6oeStqVuzjryl_DYDiipwuHqIK0M",
	}

	requestBody := CreateOrderRequest{
		ProductID:   "prod_stripe_001",
		ProductDesc: "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
		PriceID:     "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
		Quantity:    1,
		PSPProvider: "stripe",
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 || resp.StatusCode == 201 {
		var orderResp CreateOrderResponse
		if err := json.Unmarshal(body, &orderResp); err == nil {
			fmt.Printf("    Order ID: %s\n", orderResp.OrderID)
			fmt.Printf("    Amount: %.2f %s\n", orderResp.Amount, orderResp.Currency)
		}
		fmt.Printf("    Response: %s\n", string(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Order created successfully",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testGetUserOrdersExternal 测试外网获取用户订单接口
func testGetUserOrdersExternal(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Get User Orders (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/order-service/orders?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id":    "test_get_user_orders_001",
		"x-user-id":     "user123",
		"x-role":        "customer",
		"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.eB4reqDA4b8-pjK6oeStqVuzjryl_DYDiipwuHqIK0M",
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", string(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "User orders retrieved successfully",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testListAllOrdersGin 测试内网获取批量订单接口 (Gin HTTP)
func testListAllOrdersGin(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "List All Orders (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/order-service/orders?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id": "test_list_all_orders_gin_001",
		"X-User-ID":  "admin123",
		"X-Role":     "admin",
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", string(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "All orders retrieved successfully via Gin HTTP",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testListAllOrdersRPC 测试内网获取批量订单接口 (Dubbo RPC)
func testListAllOrdersRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "List All Orders (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.OrderService/ListAllOrders"
	headers := map[string]string{
		"x-trace-id": "test_list_all_orders_rpc_001",
	}

	// 构造 RPC 请求体
	requestBody := map[string]interface{}{
		"filter": map[string]interface{}{
			"limit": 50,
		},
		"pagination": map[string]interface{}{
			"limit":  50,
			"offset": 0,
		},
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("    Response: %s\n", string(body))
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "All orders retrieved successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// printTestSummary 打印测试结果汇总
func printTestSummary(results []TestResult) {
	fmt.Println("\n=== Test Summary ===")

	successCount := 0
	totalCount := len(results)

	for _, result := range results {
		status := "❌ FAIL"
		if result.Success {
			status = "✅ PASS"
			successCount++
		}

		fmt.Printf("%s %s (%.2fs) - %s\n",
			status,
			result.TestName,
			result.Duration.Seconds(),
			result.Message)
	}

	fmt.Printf("\nResults: %d/%d tests passed\n", successCount, totalCount)
	if successCount == totalCount {
		fmt.Println("🎉 All tests passed!")
	} else {
		fmt.Printf("⚠️  %d tests failed\n", totalCount-successCount)
	}
}
